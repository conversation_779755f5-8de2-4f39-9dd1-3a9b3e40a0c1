import os
import json
import joblib
import numpy as np
import pandas as pd
from collections import Counter
from datetime import datetime
from flask import Flask, request, jsonify
from flask_restx import Api, Resource, fields
from sentence_transformers import SentenceTransformer
import warnings
warnings.filterwarnings('ignore')

class ModelLoader:
    def __init__(self, models_path):
        """
        Carga todos los modelos y transformadores necesarios
        
        Args:
            models_path: Ruta donde están guardados los modelos
        """
        self.models_path = models_path
        self.models_cat = {}
        self.models_com = {}
        self.tfidf = None
        self.svd = None
        self.le_cat = None
        self.le_com = None
        self.embedding_model = None
        self.metadata = None
        
        self._load_all_models()
    
    def _load_all_models(self):
        """Carga todos los modelos y transformadores"""
        print("🔄 Cargando modelos...")
        
        # Cargar modelos de clasificación para categorías
        model_names = ['logreg_cat', 'lgb_cat', 'xgb_cat', 'rf_cat', 'ensemble_cat']
        for model_name in model_names:
            try:
                model_path = os.path.join(self.models_path, f'{model_name}.pkl')
                self.models_cat[model_name] = joblib.load(model_path)
                print(f"✓ {model_name} cargado")
            except Exception as e:
                print(f"✗ Error cargando {model_name}: {str(e)}")
        
        # Cargar modelos de clasificación para nombres comerciales
        model_names_com = ['logreg_com', 'lgb_com', 'xgb_com', 'rf_com', 'ensemble_com']
        for model_name in model_names_com:
            try:
                model_path = os.path.join(self.models_path, f'{model_name}.pkl')
                self.models_com[model_name] = joblib.load(model_path)
                print(f"✓ {model_name} cargado")
            except Exception as e:
                print(f"✗ Error cargando {model_name}: {str(e)}")
        
        # Cargar transformadores
        try:
            self.tfidf = joblib.load(os.path.join(self.models_path, 'tfidf_vectorizer.pkl'))
            print("✓ TF-IDF Vectorizer cargado")
        except Exception as e:
            print(f"✗ Error cargando TF-IDF: {str(e)}")
        
        try:
            self.svd = joblib.load(os.path.join(self.models_path, 'svd_transformer.pkl'))
            print("✓ SVD Transformer cargado")
        except Exception as e:
            print(f"✗ Error cargando SVD: {str(e)}")
        
        # Cargar encoders
        try:
            self.le_cat = joblib.load(os.path.join(self.models_path, 'label_encoder_category.pkl'))
            print("✓ Label Encoder Category cargado")
        except Exception as e:
            print(f"✗ Error cargando Label Encoder Category: {str(e)}")
        
        try:
            self.le_com = joblib.load(os.path.join(self.models_path, 'label_encoder_comercial.pkl'))
            print("✓ Label Encoder Comercial cargado")
        except Exception as e:
            print(f"✗ Error cargando Label Encoder Comercial: {str(e)}")
        
        # Cargar modelo de embeddings
        try:
            with open(os.path.join(self.models_path, 'embedding_info.json'), 'r') as f:
                embed_info = json.load(f)
            self.embedding_model = SentenceTransformer(embed_info['model_name'])
            print(f"✓ Modelo de embeddings {embed_info['model_name']} cargado")
        except Exception as e:
            print(f"✗ Error cargando modelo de embeddings: {str(e)}")
        
        # Cargar metadata
        try:
            with open(os.path.join(self.models_path, 'metadata.json'), 'r') as f:
                self.metadata = json.load(f)
            print("✓ Metadata cargada")
        except Exception as e:
            print(f"✗ Error cargando metadata: {str(e)}")
        
        print("="*50)
        print("✅ Todos los modelos cargados exitosamente!")
    
    def preprocess_text(self, name, vendor):
        """
        Preprocesa el texto combinando name y vendor
        
        Args:
            name: Nombre del producto
            vendor: Vendedor/Marca
            
        Returns:
            Features procesadas listas para predicción
        """
        # Combinar name y vendor
        combined_text = f"{name} {vendor}".strip()
        
        # Generar embeddings
        embeddings = self.embedding_model.encode([combined_text])
        
        # Aplicar TF-IDF
        tfidf_features = self.tfidf.transform([combined_text])
        
        # Aplicar SVD
        tfidf_reduced = self.svd.transform(tfidf_features.toarray())
        
        # Concatenar features
        final_features = np.hstack([embeddings, tfidf_reduced])
        
        return final_features
    
    def predict_category(self, name, vendor):
        """
        Predice la categoría usando voting de los 5 modelos
        
        Args:
            name: Nombre del producto
            vendor: Vendedor/Marca
            
        Returns:
            Predicción de categoría más votada
        """
        features = self.preprocess_text(name, vendor)
        predictions = []
        
        # Obtener predicciones de todos los modelos
        for model_name, model in self.models_cat.items():
            try:
                pred = model.predict(features)[0]
                predictions.append(pred)
            except Exception as e:
                print(f"Error en predicción de {model_name}: {str(e)}")
        
        # Voting - seleccionar la predicción más frecuente
        if predictions:
            most_common_pred = Counter(predictions).most_common(1)[0][0]
            # Decodificar la predicción
            category = self.le_cat.inverse_transform([most_common_pred])[0]
            return category
        else:
            return "Error en predicción"
    
    def predict_comercial_name(self, name, vendor):
        """
        Predice el nombre comercial usando voting de los 5 modelos
        
        Args:
            name: Nombre del producto
            vendor: Vendedor/Marca
            
        Returns:
            Predicción de nombre comercial más votada
        """
        features = self.preprocess_text(name, vendor)
        predictions = []
        
        # Obtener predicciones de todos los modelos
        for model_name, model in self.models_com.items():
            try:
                pred = model.predict(features)[0]
                predictions.append(pred)
            except Exception as e:
                print(f"Error en predicción de {model_name}: {str(e)}")
        
        # Voting - seleccionar la predicción más frecuente
        if predictions:
            most_common_pred = Counter(predictions).most_common(1)[0][0]
            # Decodificar la predicción
            comercial_name = self.le_com.inverse_transform([most_common_pred])[0]
            return comercial_name
        else:
            return "Error en predicción"
    
    def predict_both(self, name, vendor):
        """
        Predice tanto categoría como nombre comercial
        
        Args:
            name: Nombre del producto
            vendor: Vendedor/Marca
            
        Returns:
            Dict con ambas predicciones
        """
        return {
            'category': self.predict_category(name, vendor),
            'comercial_name': self.predict_comercial_name(name, vendor)
        }

# Configuración de Flask y Swagger
app = Flask(__name__)
api = Api(app, 
          version='1.0', 
          title='Product Classifier API',
          description='API para clasificación de software usando múltiples modelos ML',
          doc='/swagger/')

# Namespace para las rutas
ns = api.namespace('classifier', description='Clasificacion de software')

# Modelos para Swagger
prediction_input = api.model('PredictionInput', {
    'name': fields.String(required=True, description='Nombre del producto', example='CrowdStrike Windows Sensor'),
    'vendor': fields.String(required=True, description='Proveedor', example='CrowdStrike, Inc.')
})

prediction_output = api.model('PredictionOutput', {
    'category': fields.String(description='Categoría'),
    'comercial_name': fields.String(description='Nombre comercial')
})

category_output = api.model('CategoryOutput', {
    'category': fields.String(description='Categoría')
})

comercial_output = api.model('ComercialOutput', {
    'comercial_name': fields.String(description='Nombre comercial')
})

# Inicializar el cargador de modelos (ajusta la ruta según tu configuración)
MODELS_PATH = 'models'
model_loader = None

def initialize_models():
    """Inicializa los modelos al arrancar la aplicación"""
    global model_loader
    try:
        model_loader = ModelLoader(MODELS_PATH)
        print("🚀 API lista para recibir peticiones!")
    except Exception as e:
        print(f"❌ Error inicializando modelos: {str(e)}")

@ns.route('/predict')
class PredictBoth(Resource):
    @ns.expect(prediction_input)
    @ns.marshal_with(prediction_output)
    def post(self):
        """Predice tanto categoría como nombre comercial"""
        try:
            data = request.json
            name = data.get('name', '')
            vendor = data.get('vendor', '')
            
            if not name or not vendor:
                return {'error': 'Se requieren tanto name como vendor'}, 400
            
            predictions = model_loader.predict_both(name, vendor)
            
            return {
                'category': predictions['category'],
                'comercial_name': predictions['comercial_name']
            }
            
        except Exception as e:
            return {'error': f'Error en predicción: {str(e)}'}, 500

@ns.route('/predict/category')
class PredictCategory(Resource):
    @ns.expect(prediction_input)
    @ns.marshal_with(category_output)
    def post(self):
        """Predice solo la categoría"""
        try:
            data = request.json
            name = data.get('name', '')
            vendor = data.get('vendor', '')
            
            if not name or not vendor:
                return {'error': 'Se requieren tanto name como vendor'}, 400
            
            category = model_loader.predict_category(name, vendor)
            
            return {
                'category': category,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'error': f'Error en predicción: {str(e)}'}, 500

@ns.route('/predict/comercial')
class PredictComercial(Resource):
    @ns.expect(prediction_input)
    @ns.marshal_with(comercial_output)
    def post(self):
        """Predice solo el nombre comercial"""
        try:
            data = request.json
            name = data.get('name', '')
            vendor = data.get('vendor', '')
            
            if not name or not vendor:
                return {'error': 'Se requieren tanto name como vendor'}, 400
            
            comercial_name = model_loader.predict_comercial_name(name, vendor)
            
            return {
                'comercial_name': comercial_name,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'error': f'Error en predicción: {str(e)}'}, 500

@ns.route('/health')
class Health(Resource):
    def get(self):
        """Endpoint de salud de la API"""
        return {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'models_loaded': model_loader is not None
        }


if __name__ == '__main__':
    # Inicializar modelos
    initialize_models()
    
    # Ejecutar la API
    app.run(debug=True, host='0.0.0.0', port=5000)