# API Authentication Testing Guide

This guide covers testing the API authentication implementation to ensure all endpoints are properly protected.

## Prerequisites

- Server running on `http://localhost:3000`
- `curl` command available
- Basic understanding of HTTP cookies

## Test Scenarios

### 1. Test Unauthenticated API Access

All protected API endpoints should return 401 when accessed without authentication:

```bash
# Test data endpoint
curl -i http://localhost:3000/api/data
# Expected: HTTP 401 with {"error":"Authentication required",...}

# Test tenants endpoint
curl -i http://localhost:3000/api/tenants
# Expected: HTTP 401 with {"error":"Authentication required",...}

# Test feedback endpoint
curl -i -X POST http://localhost:3000/api/feedback \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
# Expected: HTTP 401 with {"error":"Authentication required",...}

# Test classify endpoint
curl -i -X POST http://localhost:3000/api/classify \
  -H "Content-Type: application/json" \
  -d '{"name": "test", "provider": "test"}'
# Expected: HTTP 401 with {"error":"Authentication required",...}
```

### 2. Test Authentication Endpoints (Public)

The auth endpoint should be accessible without authentication:

```bash
# Check auth status (should return false when not logged in)
curl http://localhost:3000/api/auth
# Expected: {"authenticated":false}

# Test invalid login
curl -X POST http://localhost:3000/api/auth \
  -H "Content-Type: application/json" \
  -d '{"username": "wrong", "password": "wrong"}'
# Expected: {"success":false,"message":"Invalid username or password"}

# Test valid login (save cookies)
curl -c cookies.txt -X POST http://localhost:3000/api/auth \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password123"}'
# Expected: {"success":true,"message":"Login successful"}

# Check auth status with cookie (should return true)
curl -b cookies.txt http://localhost:3000/api/auth
# Expected: {"authenticated":true}
```

### 3. Test Authenticated API Access

After logging in, all API endpoints should work:

```bash
# Test data endpoint with authentication
curl -b cookies.txt http://localhost:3000/api/data
# Expected: JSON with commercialNames and categories arrays

# Test tenants endpoint with authentication
curl -b cookies.txt http://localhost:3000/api/tenants
# Expected: JSON array of tenant objects (or 404 if no data directory)

# Test feedback GET with authentication
curl -b cookies.txt "http://localhost:3000/api/feedback?tenantId=test"
# Expected: JSON with correct and edited arrays
```

### 4. Test Logout

```bash
# Logout
curl -b cookies.txt -X POST http://localhost:3000/api/auth \
  -H "Content-Type: application/json" \
  -d '{"action": "logout"}'
# Expected: {"success":true,"message":"Logged out successfully"}

# Verify logout worked (should return 401 again)
curl -i -b cookies.txt http://localhost:3000/api/tenants
# Expected: HTTP 401 with {"error":"Authentication required",...}
```

## Automated Test Script

Create a test script to run all tests:

```bash
#!/bin/bash
# api-auth-test.sh

echo "Testing API Authentication..."

# Clean up any existing cookies
rm -f cookies.txt

echo "1. Testing unauthenticated access..."
if curl -s http://localhost:3000/api/tenants | grep -q "Authentication required"; then
    echo "✅ Unauthenticated access properly blocked"
else
    echo "❌ Unauthenticated access not blocked"
fi

echo "2. Testing login..."
if curl -s -c cookies.txt -X POST http://localhost:3000/api/auth \
    -H "Content-Type: application/json" \
    -d '{"username": "admin", "password": "password123"}' | grep -q "success.*true"; then
    echo "✅ Login successful"
else
    echo "❌ Login failed"
fi

echo "3. Testing authenticated access..."
if curl -s -b cookies.txt http://localhost:3000/api/auth | grep -q "authenticated.*true"; then
    echo "✅ Authentication verified"
else
    echo "❌ Authentication verification failed"
fi

echo "4. Testing logout..."
if curl -s -b cookies.txt -X POST http://localhost:3000/api/auth \
    -H "Content-Type: application/json" \
    -d '{"action": "logout"}' | grep -q "success.*true"; then
    echo "✅ Logout successful"
else
    echo "❌ Logout failed"
fi

echo "5. Testing post-logout access..."
if curl -s -b cookies.txt http://localhost:3000/api/tenants | grep -q "Authentication required"; then
    echo "✅ Post-logout access properly blocked"
else
    echo "❌ Post-logout access not blocked"
fi

# Clean up
rm -f cookies.txt
echo "Testing complete!"
```

## Expected Results

✅ **Working Correctly**:
- All API routes except `/api/auth` return 401 when unauthenticated
- Login with valid credentials returns success and sets cookie
- Authenticated requests work properly
- Logout clears session and blocks subsequent requests
- Auth status endpoint works correctly

❌ **Common Issues**:
- API routes accessible without authentication
- Login not setting cookies properly
- Session not persisting across requests
- Logout not clearing session

## Security Verification

1. **Cookie Security**: Verify cookies are HTTP-only and secure in production
2. **Session Management**: Ensure sessions expire appropriately
3. **Error Messages**: Confirm no sensitive information in error responses
4. **Rate Limiting**: Consider implementing rate limiting for auth endpoints
