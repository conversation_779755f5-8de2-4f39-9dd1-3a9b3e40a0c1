# Authentication Setup

This Next.js application now includes simple username/password authentication to restrict access to the frontend.

## Features

- **Simple Login**: Username and password authentication
- **Session Management**: HTTP-only cookies for security
- **Route Protection**: Middleware protects all routes except login
- **API Protection**: All API routes require authentication except `/api/auth`
- **Automatic Redirects**: Unauthenticated users are redirected to login
- **Logout Functionality**: Logout button in the header

## Default Credentials

- **Username**: `admin`
- **Password**: `password123`

## Configuration

### Environment Variables

You can customize the credentials by setting environment variables:

1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Edit `.env.local` with your desired credentials:
   ```
   AUTH_USERNAME=your_username
   AUTH_PASSWORD=your_secure_password
   ```

### Security Notes

⚠️ **Important**: This is a simple authentication implementation suitable for basic access control.

## Files Added/Modified

### New Files
- `middleware.ts` - Route protection middleware
- `app/login/page.tsx` - Login page component
- `app/api/auth/route.ts` - Authentication API endpoints
- `lib/auth-context.tsx` - Authentication context provider
- `lib/auth-utils.ts` - Authentication utilities for API routes
- `components/auth-loading.tsx` - Loading component
- `.env.example` - Environment variables template

### Modified Files
- `app/layout.tsx` - Added AuthProvider wrapper
- `app/page.tsx` - Added logout button and auth loading state
- `app/api/data/route.ts` - Added authentication requirement
- `app/api/tenants/route.ts` - Added authentication requirement
- `app/api/tenants/[tenantId]/software-inventory/route.ts` - Added authentication requirement
- `app/api/feedback/route.ts` - Added authentication requirement
- `app/api/classify/route.ts` - Added authentication requirement

## How It Works

1. **Middleware**: Checks for authentication cookie on page requests
2. **Login Page**: Validates credentials and sets secure cookie
3. **Auth Context**: Manages authentication state across the app
4. **Protected Routes**: All routes except `/login` require authentication
5. **Protected APIs**: All API routes except `/api/auth` require authentication
6. **Auth Utils**: Shared authentication utilities for API route protection
7. **Logout**: Clears authentication cookie and redirects to login

## Usage

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to `http://localhost:3000`
3. You'll be redirected to the login page
4. Enter the default credentials (admin/password123)
5. You'll be redirected to the main application
6. Use the logout button in the header to sign out

## API Endpoints

### Authentication Endpoints (Public)
- `POST /api/auth` - Login with username/password
- `GET /api/auth` - Check authentication status
- `POST /api/auth` with `action: 'logout'` - Logout

### Protected API Endpoints (Require Authentication)
- `GET /api/data` - Get commercial names and categories
- `GET /api/tenants` - Get list of tenants
- `GET /api/tenants/[tenantId]/software-inventory` - Get software inventory for tenant
- `POST /api/feedback` - Submit feedback
- `GET /api/feedback` - Get feedback data
- `DELETE /api/feedback` - Delete feedback
- `POST /api/classify` - Classify software

### Testing API Authentication

**Test unauthenticated access (should return 401):**
```bash
curl http://localhost:3000/api/tenants
# Returns: {"error":"Authentication required","message":"You must be logged in to access this resource"}
```

**Test with authentication:**
```bash
# First login to get session cookie
curl -c cookies.txt -X POST http://localhost:3000/api/auth \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password123"}'

# Then use the cookie for API requests
curl -b cookies.txt http://localhost:3000/api/tenants
```

## Customization

To modify the authentication logic:

1. **Change credentials**: Update environment variables
2. **Modify UI**: Edit `app/login/page.tsx`
3. **Add features**: Extend `lib/auth-context.tsx`
4. **Update security**: Modify `app/api/auth/route.ts`
