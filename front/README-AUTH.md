# Authentication Setup

This Next.js application now includes simple username/password authentication to restrict access to the frontend.

## Features

- **Simple Login**: Username and password authentication
- **Session Management**: HTTP-only cookies for security
- **Route Protection**: Middleware protects all routes except login
- **Automatic Redirects**: Unauthenticated users are redirected to login
- **Logout Functionality**: Logout button in the header

## Default Credentials

- **Username**: `admin`
- **Password**: `password123`

## Configuration

### Environment Variables

You can customize the credentials by setting environment variables:

1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Edit `.env.local` with your desired credentials:
   ```
   AUTH_USERNAME=your_username
   AUTH_PASSWORD=your_secure_password
   ```

### Security Notes

⚠️ **Important**: This is a simple authentication implementation suitable for basic access control.

## Files Added/Modified

### New Files
- `middleware.ts` - Route protection middleware
- `app/login/page.tsx` - Login page component
- `app/api/auth/route.ts` - Authentication API endpoints
- `lib/auth-context.tsx` - Authentication context provider
- `components/auth-loading.tsx` - Loading component
- `.env.example` - Environment variables template

### Modified Files
- `app/layout.tsx` - Added AuthProvider wrapper
- `app/page.tsx` - Added logout button and auth loading state

## How It Works

1. **Middleware**: Checks for authentication cookie on every request
2. **Login Page**: Validates credentials and sets secure cookie
3. **Auth Context**: Manages authentication state across the app
4. **Protected Routes**: All routes except `/login` require authentication
5. **Logout**: Clears authentication cookie and redirects to login

## Usage

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to `http://localhost:3000`
3. You'll be redirected to the login page
4. Enter the default credentials (admin/password123)
5. You'll be redirected to the main application
6. Use the logout button in the header to sign out

## API Endpoints

- `POST /api/auth` - Login with username/password
- `GET /api/auth` - Check authentication status
- `POST /api/auth` with `action: 'logout'` - Logout

## Customization

To modify the authentication logic:

1. **Change credentials**: Update environment variables
2. **Modify UI**: Edit `app/login/page.tsx`
3. **Add features**: Extend `lib/auth-context.tsx`
4. **Update security**: Modify `app/api/auth/route.ts`
