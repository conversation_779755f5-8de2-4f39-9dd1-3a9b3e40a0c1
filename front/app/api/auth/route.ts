import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

// Simple hardcoded credentials - in production, use environment variables
const VALID_CREDENTIALS = {
  username: process.env.AUTH_USERNAME || 'admin',
  password: process.env.AUTH_PASSWORD || 'password123'
}

// Simple session token (in production, use proper JWT or session management)
const SESSION_TOKEN = 'authenticated-user-session'

export async function POST(request: NextRequest) {
  try {
    const { username, password, action } = await request.json()

    if (action === 'logout') {
      // Handle logout by setting cookie with expired date
      const cookieStore = await cookies()
      cookieStore.set('auth-token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 0, // Expire immediately
        path: '/'
      })

      return NextResponse.json({ success: true, message: 'Logged out successfully' })
    }

    // Handle login
    if (!username || !password) {
      return NextResponse.json(
        { success: false, message: 'Username and password are required' },
        { status: 400 }
      )
    }

    // Validate credentials
    if (username === VALID_CREDENTIALS.username && password === VALID_CREDENTIALS.password) {
      // Set authentication cookie
      const cookieStore = await cookies()
      cookieStore.set('auth-token', SESSION_TOKEN, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: '/'
      })

      return NextResponse.json({
        success: true,
        message: 'Login successful'
      })
    } else {
      return NextResponse.json(
        { success: false, message: 'Invalid username or password' },
        { status: 401 }
      )
    }
  } catch (error) {
    console.error('Auth error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const cookieStore = await cookies()
    const authToken = cookieStore.get('auth-token')

    if (authToken && authToken.value === SESSION_TOKEN) {
      return NextResponse.json({ authenticated: true })
    } else {
      return NextResponse.json({ authenticated: false })
    }
  } catch (error) {
    console.error('Auth check error:', error)
    return NextResponse.json({ authenticated: false })
  }
}
