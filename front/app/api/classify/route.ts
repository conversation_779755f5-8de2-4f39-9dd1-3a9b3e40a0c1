import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { name, provider } = await request.json()

    // Replace this with your actual classification logic/API call
    // This could call an external service, ML model, or database lookup
    const classification = await classifySoftware(name, provider)

    return NextResponse.json(classification)
  } catch (error) {
    console.error("Error classifying software:", error)
    return NextResponse.json({ error: "Failed to classify software" }, { status: 500 })
  }
}

// Mock classification function - replace with your actual implementation
async function classifySoftware(name: string, provider: string) {
  // This is where you'd implement your classification logic
  // Could be a call to an ML model, database lookup, or external API

  const classifications: Record<string, { category: string; commercialName: string }> = {
    "Microsoft Windows Desktop Runtime": {
      category: "Development Tools",
      commercialName: "Windows Runtime",
    },
    "Cortex XDR": {
      category: "EDR",
      commercialName: "Cortex XDR",
    },
    "Microsoft Office": {
      category: "Ofimatica",
      commercialName: "Microsoft Office",
    },
    "Qualys Cloud Security Agent": {
      category: "Security Software",
      commercialName: "Qualys Security",
    },
    "VLC media player": {
      category: "Multimedia",
      commercialName: "VLC Media Player",
    },
  }

  // Simple matching logic - you'd want something more sophisticated
  for (const [key, value] of Object.entries(classifications)) {
    if (name.toLowerCase().includes(key.toLowerCase())) {
      return value
    }
  }

  return {
    category: "Unknown",
    commercialName: name,
  }
}
