import { NextRequest, NextResponse } from "next/server"
import { promises as fs } from 'fs'
import path from 'path'
import { requireAuth } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) {
    return authError
  }
  try {
    // Leer archivos de listas
    const commercialNamesPath = path.join(process.cwd(), 'data', 'commercial_name_list.json')
    const categoriesPath = path.join(process.cwd(), 'data', 'category_list.json')
    const additionsPath = path.join(process.cwd(), 'data', 'commercial_names_list_additions.json')

    const [commercialNamesContent, categoriesContent, additionsContent] = await Promise.all([
      fs.readFile(commercialNamesPath, 'utf8'),
      fs.readFile(categoriesPath, 'utf8'),
      fs.readFile(additionsPath, 'utf8')
    ])

    const commercialNamesData = JSON.parse(commercialNamesContent)
    const categoriesData = JSON.parse(categoriesContent)
    const additionsData = JSON.parse(additionsContent)

    // Combinar nombres comerciales principales con adiciones
    const allCommercialNames = [
      ...commercialNamesData.commercialNames,
      ...additionsData.commercialNames
    ].sort()

    return NextResponse.json({
      commercialNames: allCommercialNames,
      categories: categoriesData.categories.sort()
    })

  } catch (error) {
    console.error("Error fetching data lists:", error)
    return NextResponse.json({ error: "Failed to fetch data lists" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const { type, value } = await request.json()

    if (!type || !value) {
      return NextResponse.json({ error: "Missing type or value" }, { status: 400 })
    }

    if (type === 'commercialName') {
      // Agregar nuevo nombre comercial a additions
      const additionsPath = path.join(process.cwd(), 'data', 'commercial_names_list_additions.json')

      const additionsContent = await fs.readFile(additionsPath, 'utf8')
      const additionsData = JSON.parse(additionsContent)

      // Verificar si ya existe
      if (!additionsData.commercialNames.includes(value)) {
        additionsData.commercialNames.push(value)
        additionsData.commercialNames.sort()

        await fs.writeFile(additionsPath, JSON.stringify(additionsData, null, 2))
      }

      return NextResponse.json({ success: true, message: "Commercial name added successfully" })
    } else {
      return NextResponse.json({ error: "Invalid type" }, { status: 400 })
    }

  } catch (error) {
    console.error("Error adding new value:", error)
    return NextResponse.json({ error: "Failed to add new value" }, { status: 500 })
  }
}
