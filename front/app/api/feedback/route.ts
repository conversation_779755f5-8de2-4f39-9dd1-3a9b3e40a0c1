import { NextRequest, NextResponse } from "next/server"
import { promises as fs } from 'fs'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, tenantId, itemId, itemData, commercialName, category } = body

    if (!type || !tenantId || !itemId || !itemData) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const reviewData = {
      id: itemId,
      tenantId,
      timestamp: new Date().toISOString(),
      originalData: itemData,
      ...((type === 'edited' && commercialName && category) && {
        editedData: {
          commercialName,
          category
        }
      })
    }

    if (type === 'correct') {
      // Guardar en inventory_correct.json
      const correctPath = path.join(process.cwd(), 'data', 'review', 'inventory_correct.json')
      const correctData = await readJSONFile(correctPath)

      // Verificar si ya existe este item para este tenant
      const existingIndex = correctData.findIndex(item => item.id === itemId && item.tenantId === tenantId)

      if (existingIndex !== -1) {
        correctData[existingIndex] = reviewData
      } else {
        correctData.push(reviewData)
      }

      await fs.writeFile(correctPath, JSON.stringify(correctData, null, 2))

    } else if (type === 'edited') {
      // Guardar en inventory_edited.json
      const editedPath = path.join(process.cwd(), 'data', 'review', 'inventory_edited.json')
      const editedData = await readJSONFile(editedPath)

      // Verificar si ya existe este item para este tenant
      const existingIndex = editedData.findIndex(item => item.id === itemId && item.tenantId === tenantId)

      if (existingIndex !== -1) {
        editedData[existingIndex] = reviewData
      } else {
        editedData.push(reviewData)
      }

      await fs.writeFile(editedPath, JSON.stringify(editedData, null, 2))
    }

    return NextResponse.json({ success: true, data: reviewData })

  } catch (error) {
    console.error("Error saving feedback:", error)
    return NextResponse.json({ error: "Failed to save feedback" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tenantId = searchParams.get('tenantId')

    if (!tenantId) {
      return NextResponse.json({ error: "Missing tenantId parameter" }, { status: 400 })
    }

    // Leer ambos archivos de feedback
    const correctPath = path.join(process.cwd(), 'data', 'review', 'inventory_correct.json')
    const editedPath = path.join(process.cwd(), 'data', 'review', 'inventory_edited.json')

    const correctData = await readJSONFile(correctPath)
    const editedData = await readJSONFile(editedPath)

    // Filtrar por tenant
    const tenantCorrect = correctData.filter(item => item.tenantId === tenantId)
    const tenantEdited = editedData.filter(item => item.tenantId === tenantId)

    return NextResponse.json({
      correct: tenantCorrect,
      edited: tenantEdited
    })

  } catch (error) {
    console.error("Error fetching feedback:", error)
    return NextResponse.json({ error: "Failed to fetch feedback" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { tenantId, itemId, type } = body

    if (!tenantId || !itemId || !type) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    if (type === 'correct') {
      const correctPath = path.join(process.cwd(), 'data', 'review', 'inventory_correct.json')
      const correctData = await readJSONFile(correctPath)
      const filteredData = correctData.filter(item => !(item.id === itemId && item.tenantId === tenantId))
      await fs.writeFile(correctPath, JSON.stringify(filteredData, null, 2))
    } else if (type === 'edited') {
      const editedPath = path.join(process.cwd(), 'data', 'review', 'inventory_edited.json')
      const editedData = await readJSONFile(editedPath)
      const filteredData = editedData.filter(item => !(item.id === itemId && item.tenantId === tenantId))
      await fs.writeFile(editedPath, JSON.stringify(filteredData, null, 2))
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error("Error deleting feedback:", error)
    return NextResponse.json({ error: "Failed to delete feedback" }, { status: 500 })
  }
}

// Helper function to read JSON files safely
async function readJSONFile(filePath: string) {
  try {
    const content = await fs.readFile(filePath, 'utf8')
    return JSON.parse(content)
  } catch (error) {
    // If file doesn't exist or is empty, return empty array
    return []
  }
}
