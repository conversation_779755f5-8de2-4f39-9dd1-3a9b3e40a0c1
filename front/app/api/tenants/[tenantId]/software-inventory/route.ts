import { type NextRequest, NextResponse } from "next/server"
import { promises as fs } from 'fs'
import path from 'path'
import { requireAuth } from '@/lib/auth-utils'

export async function GET(request: NextRequest, { params }: { params: Promise<{ tenantId: string }> }) {
  // Check authentication
  const authError = await requireAuth(request)
  if (authError) {
    return authError
  }
  try {
    const { tenantId } = await params
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const filter = searchParams.get('filter') || 'all' // Nuevo parámetro de filtro
    const offset = (page - 1) * limit

    // Construir la ruta al archivo JSON del tenant
    const filePath = path.join(process.cwd(), 'data', 'tenants', `${tenantId}.json`)

    try {
      // Leer el archivo JSON
      const fileContent = await fs.readFile(filePath, 'utf8')
      const tenantData = JSON.parse(fileContent)

      // Extraer todas las aplicaciones de todos los registros
      const allApplications: any[] = []

      if (Array.isArray(tenantData)) {
        tenantData.forEach((record: any) => {
          if (record._source && Array.isArray(record._source.Applications)) {
            record._source.Applications.forEach((app: any) => {
              allApplications.push({
                id: `${record._id}_${app.IdentifyingNumber || Math.random()}`,
                name: app.Name,
                provider: app.Vendor || 'Unknown',
                version: app.Version,
                installDate: app.InstallDate,
                category: null, // Para ser clasificado
                commercialName: null, // Para ser clasificado
                feedback: null
              })
            })
          }
        })
      }

      // Eliminar duplicados basado en Name y Vendor
      const uniqueApplications = allApplications.reduce((acc: any[], current) => {
        const isDuplicate = acc.some(app =>
          app.name === current.name && app.provider === current.provider
        )
        if (!isDuplicate) {
          acc.push(current)
        }
        return acc
      }, [])

      // Leer datos de feedback para aplicar filtros
      let feedbackData = { correct: [], edited: [] }
      try {
        const correctPath = path.join(process.cwd(), 'data', 'review', 'inventory_correct.json')
        const editedPath = path.join(process.cwd(), 'data', 'review', 'inventory_edited.json')

        const [correctContent, editedContent] = await Promise.all([
          fs.readFile(correctPath, 'utf8').catch(() => '[]'),
          fs.readFile(editedPath, 'utf8').catch(() => '[]')
        ])

        const correctData = JSON.parse(correctContent)
        const editedData = JSON.parse(editedContent)

        feedbackData.correct = correctData.filter((item: any) => item.tenantId === tenantId)
        feedbackData.edited = editedData.filter((item: any) => item.tenantId === tenantId)
      } catch (feedbackError) {
        console.error('Error reading feedback data:', feedbackError)
      }

      // Aplicar feedback a los items
      const itemsWithFeedback = uniqueApplications.map(item => {
        const correctFeedback = feedbackData.correct.find((f: any) => f.id === item.id)
        const editedFeedback = feedbackData.edited.find((f: any) => f.id === item.id)

        if (editedFeedback) {
          return {
            ...item,
            feedback: "corrected",
            commercialName: editedFeedback.editedData?.commercialName || item.commercialName,
            category: editedFeedback.editedData?.category || item.category
          }
        } else if (correctFeedback) {
          return {
            ...item,
            feedback: "positive"
          }
        }

        return item
      })

      // Aplicar filtros ANTES de la paginación
      let filteredItems = itemsWithFeedback
      if (filter === 'positive') {
        filteredItems = itemsWithFeedback.filter(item => item.feedback === 'positive')
      } else if (filter === 'negative') {
        filteredItems = itemsWithFeedback.filter(item => item.feedback === 'corrected')
      } else if (filter === 'not-reviewed') {
        filteredItems = itemsWithFeedback.filter(item => item.feedback === null)
      }

      // Aplicar paginación DESPUÉS del filtrado
      const paginatedApps = filteredItems.slice(offset, offset + limit)

      const response = {
        data: paginatedApps,
        pagination: {
          currentPage: page,
          totalItems: filteredItems.length, // Total de items filtrados, no de todos
          totalPages: Math.ceil(filteredItems.length / limit),
          itemsPerPage: limit,
          hasNextPage: offset + limit < filteredItems.length,
          hasPreviousPage: page > 1
        },
        totalCounts: {
          all: itemsWithFeedback.length,
          positive: feedbackData.correct.length,
          edited: feedbackData.edited.length,
          notReviewed: itemsWithFeedback.length - feedbackData.correct.length - feedbackData.edited.length
        }
      }

      return NextResponse.json(response)
    } catch (fileError) {
      // Si el archivo no existe, devolver array vacío o error específico
      console.error(`File not found for tenant ${tenantId}:`, fileError)
      return NextResponse.json({ error: `Tenant ${tenantId} not found` }, { status: 404 })
    }

  } catch (error) {
    console.error("Error fetching software inventory:", error)
    return NextResponse.json({ error: "Failed to fetch software inventory" }, { status: 500 })
  }
}
