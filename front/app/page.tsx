"use client"

import { useState, useEffect } from "react"
import { ChevronDown, ThumbsUp, ThumbsDown, Check, X, CheckCircle, ChevronLeft, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { SelectiveDropdown } from "@/components/ui/selective-dropdown"
import Image from "next/image"

interface SoftwareItem {
  id: string
  name: string
  provider: string
  version?: string
  commercialName?: string
  category?: string
  feedback?: "positive" | "negative" | "corrected" | null
}

interface EditingItem {
  id: string
  commercialName: string
  category: string
}

interface Tenant {
  id: string
  name: string
}

interface PaginationData {
  currentPage: number
  totalItems: number
  totalPages: number
  itemsPerPage: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

interface TotalCounts {
  all: number
  positive: number
  edited: number
  notReviewed: number
}

interface DataLists {
  commercialNames: string[]
  categories: string[]
}

export default function SoftwareClassifier() {
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null)
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [softwareItems, setSoftwareItems] = useState<SoftwareItem[]>([])
  const [pagination, setPagination] = useState<PaginationData | null>(null)
  const [totalCounts, setTotalCounts] = useState<TotalCounts>({ all: 0, positive: 0, edited: 0, notReviewed: 0 })
  const [loading, setLoading] = useState(false)
  const [tenantsLoading, setTenantsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [filter, setFilter] = useState<"all" | "positive" | "negative" | "not-reviewed">("all")
  const [editingItem, setEditingItem] = useState<EditingItem | null>(null)
  
  // Filter states
  const [commercialNameFilter, setCommercialNameFilter] = useState<string>("all")
  const [categoryFilter, setCategoryFilter] = useState<string>("all")
  
  // Data lists
  const [dataLists, setDataLists] = useState<DataLists>({ commercialNames: [], categories: [] })
  const [dataListsLoading, setDataListsLoading] = useState(true)
  
  // Error states
  const [editError, setEditError] = useState<string>("")

  // Fetch data lists
  const fetchDataLists = async () => {
    setDataListsLoading(true)
    try {
      const response = await fetch('/api/data')
      if (response.ok) {
        const data = await response.json()
        setDataLists(data)
      } else {
        console.error('Error fetching data lists:', response.statusText)
      }
    } catch (error) {
      console.error('Error fetching data lists:', error)
    } finally {
      setDataListsLoading(false)
    }
  }

  // Add new commercial name
  const addNewCommercialName = async (value: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'commercialName',
          value: value
        }),
      })

      if (response.ok) {
        // Refresh data lists
        await fetchDataLists()
        return true
      }
      return false
    } catch (error) {
      console.error('Error adding commercial name:', error)
      return false
    }
  }

  // Fetch tenants list
  const fetchTenants = async () => {
    setTenantsLoading(true)
    try {
      const response = await fetch('/api/tenants')
      if (response.ok) {
        const data = await response.json()
        setTenants(data)
        // Seleccionar el primer tenant por defecto
        if (data.length > 0) {
          setSelectedTenant(data[0])
        }
      } else {
        console.error('Error fetching tenants:', response.statusText)
      }
    } catch (error) {
      console.error('Error fetching tenants:', error)
    } finally {
      setTenantsLoading(false)
    }
  }

  // Fetch software inventory for selected tenant
  const fetchSoftwareInventory = async (tenantId: string, page: number = 1, currentFilter: string = "all") => {
    setLoading(true)
    try {
      const response = await fetch(`/api/tenants/${tenantId}/software-inventory?page=${page}&limit=10&filter=${currentFilter}`)
      
      if (response.ok) {
        const data = await response.json()
        setSoftwareItems(data.data)
        setPagination(data.pagination)
        setTotalCounts(data.totalCounts)
      } else {
        console.error('Error fetching software inventory:', response.statusText)
        setSoftwareItems([])
        setPagination(null)
        setTotalCounts({ all: 0, positive: 0, edited: 0, notReviewed: 0 })
      }
    } catch (error) {
      console.error('Error fetching software inventory:', error)
      setSoftwareItems([])
      setPagination(null)
      setTotalCounts({ all: 0, positive: 0, edited: 0, notReviewed: 0 })
    } finally {
      setLoading(false)
    }
  }

  // Save feedback to API
  const saveFeedback = async (type: 'correct' | 'edited', itemId: string, itemData: any, editedData?: any) => {
    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          tenantId: selectedTenant?.id,
          itemId,
          itemData,
          ...(editedData && {
            commercialName: editedData.commercialName,
            category: editedData.category
          })
        }),
      })

      if (response.ok) {
        // Refresh inventory data to get updated counts and feedback
        if (selectedTenant) {
          await fetchSoftwareInventory(selectedTenant.id, currentPage, filter)
        }
        return true
      }
    } catch (error) {
      console.error('Error saving feedback:', error)
    }
    return false
  }

  // Remove feedback from API
  const removeFeedback = async (type: 'correct' | 'edited', itemId: string) => {
    try {
      const response = await fetch('/api/feedback', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId: selectedTenant?.id,
          itemId,
          type
        }),
      })

      if (response.ok) {
        // Refresh inventory data to get updated counts and feedback
        if (selectedTenant) {
          await fetchSoftwareInventory(selectedTenant.id, currentPage, filter)
        }
        return true
      }
    } catch (error) {
      console.error('Error removing feedback:', error)
    }
    return false
  }

  // Handle tenant selection
  const handleTenantSelect = (tenant: Tenant) => {
    setSelectedTenant(tenant)
    setCurrentPage(1)
    setFilter("all")
    setCommercialNameFilter("all")
    setCategoryFilter("all")
    setTotalCounts({ all: 0, positive: 0, edited: 0, notReviewed: 0 })
  }

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage)
  }

  // Handle filter change
  const handleFilterChange = (newFilter: "all" | "positive" | "negative" | "not-reviewed") => {
    if (filter === newFilter) {
      setFilter("all")
      setCurrentPage(1)
    } else {
      setFilter(newFilter)
      setCurrentPage(1) // Reset to first page when changing filter
    }
  }

  // Handle feedback interaction
  const handleFeedback = async (itemId: string, feedbackType: "positive" | "negative") => {
    const item = softwareItems.find((item) => item.id === itemId)
    if (!item) return

    if (feedbackType === "negative") {
      // Start editing mode for thumbs down
      setEditingItem({
        id: itemId,
        commercialName: item.commercialName || "",
        category: item.category || "",
      })
      setEditError("") // Clear any previous errors
    } else {
      // Handle positive feedback
      if (item.feedback === "positive") {
        // Remove positive feedback
        await removeFeedback('correct', itemId)
      } else {
        // Add positive feedback
        await saveFeedback('correct', itemId, {
          name: item.name,
          provider: item.provider,
          version: item.version
        })
      }
    }
  }

  // Handle editing field changes
  const handleEditChange = (field: "commercialName" | "category", value: string) => {
    if (editingItem) {
      setEditingItem({
        ...editingItem,
        [field]: value,
      })
      
      // Clear error when user starts typing
      if (editError) {
        setEditError("")
      }
    }
  }

  // Confirm editing changes
  const confirmEdit = async () => {
    if (!editingItem) return

    // Validate category is from the allowed list
    if (editingItem.category && !dataLists.categories.includes(editingItem.category)) {
      setEditError("Please select a valid category from the list")
      return
    }

    const item = softwareItems.find((item) => item.id === editingItem.id)
    if (!item) return

    const success = await saveFeedback('edited', editingItem.id, {
      name: item.name,
      provider: item.provider,
      version: item.version
    }, {
      commercialName: editingItem.commercialName,
      category: editingItem.category
    })

    if (success) {
      // Reset editing state
      setEditingItem(null)
      setEditError("")
    }
  }

  // Cancel editing
  const cancelEdit = () => {
    setEditingItem(null)
    setEditError("")
  }

  // Load initial data
  useEffect(() => {
    fetchDataLists()
    fetchTenants()
  }, [])

  // Load software inventory when tenant, page, or filter changes
  useEffect(() => {
    if (selectedTenant) {
      fetchSoftwareInventory(selectedTenant.id, currentPage, filter)
    }
  }, [selectedTenant, currentPage, filter])

  // Note: We now use the complete lists from JSON files for filters
  // instead of just the values present in the current tenant data

  // Apply client-side filters for additional filtering beyond API
  const clientFilteredItems = softwareItems.filter((item) => {
    // Commercial Name filter
    const commercialNameMatch = commercialNameFilter === "all" || item.commercialName === commercialNameFilter
    // Category filter
    const categoryMatch = categoryFilter === "all" || item.category === categoryFilter
    
    return commercialNameMatch && categoryMatch
  })

  // Reset page when additional filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [commercialNameFilter, categoryFilter])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="img-logo"><Image src="/batuta-logo-dark.svg" alt="logo" width={100} height={100} /></span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="px-6 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Title and Tenant Selector */}
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-2xl font-semibold text-gray-900">Software Classifier</h1>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="bg-gray-800 text-white hover:bg-gray-700 border-gray-800" disabled={tenantsLoading}>
                  {tenantsLoading ? "Loading..." : selectedTenant ? selectedTenant.name : "Select Tenant"}
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px] max-h-[300px] overflow-y-auto">
                {tenants.map((tenant) => (
                  <DropdownMenuItem 
                    key={tenant.id} 
                    onClick={() => handleTenantSelect(tenant)}
                    className={selectedTenant?.id === tenant.id ? "bg-gray-100" : ""}
                  >
                    {tenant.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Additional Filters */}
          <div className="flex space-x-4 mb-6">
            <div className="flex flex-col">
              <label className="text-sm font-medium text-gray-700 mb-2">Commercial Name</label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="w-48 flex justify-between items-center"
                    disabled={dataListsLoading}
                  >
                    <span className="truncate">
                      {dataListsLoading ? "Loading..." : commercialNameFilter === "all" ? "All Commercial Names" : commercialNameFilter}
                    </span>
                    <ChevronDown className="h-4 w-4 ml-2 flex-shrink-0" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-48 max-h-60 overflow-y-auto">
                  <DropdownMenuItem onClick={() => setCommercialNameFilter("all")} className="cursor-pointer">
                    All Commercial Names
                  </DropdownMenuItem>
                  {dataLists.commercialNames.map((name) => (
                    <DropdownMenuItem
                      key={name}
                      onClick={() => setCommercialNameFilter(name)}
                      className="cursor-pointer"
                    >
                      {name}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="flex flex-col">
              <label className="text-sm font-medium text-gray-700 mb-2">Category</label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="w-48 flex justify-between items-center"
                    disabled={dataListsLoading}
                  >
                    <span className="truncate">
                      {dataListsLoading ? "Loading..." : categoryFilter === "all" ? "All Categories" : categoryFilter}
                    </span>
                    <ChevronDown className="h-4 w-4 ml-2 flex-shrink-0" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-48 max-h-60 overflow-y-auto">
                  <DropdownMenuItem onClick={() => setCategoryFilter("all")} className="cursor-pointer">
                    All Categories
                  </DropdownMenuItem>
                  {dataLists.categories.map((category) => (
                    <DropdownMenuItem
                      key={category}
                      onClick={() => setCategoryFilter(category)}
                      className="cursor-pointer"
                    >
                      {category}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Clear Filters Button */}
            {(commercialNameFilter !== "all" || categoryFilter !== "all") && (
              <div className="flex flex-col justify-end">
                <Button
                  variant="ghost"
                  onClick={() => {
                    setCommercialNameFilter("all")
                    setCategoryFilter("all")
                  }}
                  className="text-gray-500 hover:text-gray-700"
                  disabled={dataListsLoading}
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>

          {/* Status Filter Buttons */}
          <div className="flex space-x-2 mb-6">
            <Button
              variant={filter === "positive" ? "default" : "secondary"}
              onClick={() => handleFilterChange("positive")}
              className={`${filter === "positive"
                ? "bg-gray-800 text-white hover:bg-gray-700"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
            >
              Positive {totalCounts.positive}
            </Button>
            <Button
              variant={filter === "negative" ? "default" : "secondary"}
              onClick={() => handleFilterChange("negative")}
              className={`${filter === "negative"
                ? "bg-gray-800 text-white hover:bg-gray-700"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
            >
              Edited {totalCounts.edited}
            </Button>
            <Button
              variant={filter === "not-reviewed" ? "default" : "secondary"}
              onClick={() => handleFilterChange("not-reviewed")}
              className={`${filter === "not-reviewed"
                ? "bg-gray-800 text-white hover:bg-gray-700"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
            >
              Not Reviewed {totalCounts.notReviewed}
            </Button>
          </div>

          {/* Error Message */}
          {editError && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{editError}</p>
            </div>
          )}

          {/* Software Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vendor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Commercial Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Feedback
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                        Loading software inventory...
                      </td>
                    </tr>
                  ) : !selectedTenant ? (
                    <tr>
                      <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                        Please select a tenant to view software inventory
                      </td>
                    </tr>
                  ) : clientFilteredItems.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                        No software items found for this filter
                      </td>
                    </tr>
                  ) : (
                    clientFilteredItems.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.name}>
                          {item.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">{item.provider}</td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {editingItem?.id === item.id ? (
                            <SelectiveDropdown
                              value={editingItem.commercialName}
                              onChange={(value) => handleEditChange("commercialName", value)}
                              options={dataLists.commercialNames}
                              placeholder="Select commercial name"
                              allowNew={true}
                              onAddNew={addNewCommercialName}
                              className="w-full"
                            />
                          ) : (
                            item.commercialName || "-"
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {editingItem?.id === item.id ? (
                            <SelectiveDropdown
                              value={editingItem.category}
                              onChange={(value) => handleEditChange("category", value)}
                              options={dataLists.categories}
                              placeholder="Select category"
                              strict={true}
                              className="w-full"
                            />
                          ) : (
                            item.category || "-"
                          )}
                        </td>
                        <td className="px-6 py-4">
                          {editingItem?.id === item.id ? (
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                onClick={confirmEdit}
                                className="p-2 rounded-full bg-green-100 text-green-600 hover:bg-green-200"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={cancelEdit}
                                className="p-2 rounded-full text-gray-400 hover:text-red-600 hover:bg-red-50"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex space-x-2">
                              {/* Only show thumbs up if not corrected */}
                              {item.feedback !== "corrected" && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleFeedback(item.id, "positive")}
                                  className={`p-2 rounded-full ${item.feedback === "positive"
                                    ? "bg-green-100 text-green-600 hover:bg-green-200"
                                    : "text-gray-400 hover:text-green-600 hover:bg-green-50"
                                    }`}
                                >
                                  <ThumbsUp className="h-4 w-4" />
                                </Button>
                              )}
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleFeedback(item.id, "negative")}
                                className={`p-2 rounded-full ${item.feedback === "negative"
                                  ? "bg-red-100 text-red-600 hover:bg-red-200"
                                  : "text-gray-400 hover:text-red-600 hover:bg-red-50"
                                  }`}
                              >
                                <ThumbsDown className="h-4 w-4" />
                              </Button>
                              {item.feedback === "corrected" && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="p-2 rounded-full bg-blue-100 text-blue-600 cursor-default"
                                  disabled
                                >
                                  <CheckCircle className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          )}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-700">
                Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{" "}
                {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{" "}
                {pagination.totalItems} results
                {filter !== "all" && (
                  <span className="ml-1 text-gray-500">
                    (filtered from {totalCounts.all} total)
                  </span>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={!pagination.hasPreviousPage}
                  className="flex items-center"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.currentPage >= pagination.totalPages - 2) {
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      pageNum = pagination.currentPage - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === pagination.currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className={`w-8 h-8 p-0 ${pageNum === pagination.currentPage
                          ? "bg-gray-800 text-white"
                          : "text-gray-700"
                          }`}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className="flex items-center"
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
