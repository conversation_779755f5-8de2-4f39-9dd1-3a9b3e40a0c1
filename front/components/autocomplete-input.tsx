"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { PlusCircle } from "lucide-react"

interface AutocompleteInputProps {
  value: string
  onChange: (value: string) => void
  onSelect: (value: string) => void
  options: string[]
  placeholder?: string
  allowNewValues?: boolean
  onAddNewValue?: (value: string) => void
}

export function AutocompleteInput({
  value,
  onChange,
  onSelect,
  options,
  placeholder,
  allowNewValues = false,
  onAddNewValue,
}: AutocompleteInputProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Filter options based on input value
  const filteredOptions = options.filter((option) => option.toLowerCase().includes(value.toLowerCase())).slice(0, 10) // Limit to 10 options for performance

  // Check if the current value exists in options
  const valueExists = options.some((option) => option.toLowerCase() === value.toLowerCase())
  const showAddNew = allowNewValues && value.trim() !== "" && !valueExists

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowDown") {
      e.preventDefault()
      setIsOpen(true)
      setHighlightedIndex((prev) => Math.min(prev + 1, filteredOptions.length - 1))
    } else if (e.key === "ArrowUp") {
      e.preventDefault()
      setHighlightedIndex((prev) => Math.max(prev - 1, -1))
    } else if (e.key === "Enter") {
      e.preventDefault()
      if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
        handleSelect(filteredOptions[highlightedIndex])
      } else if (showAddNew && onAddNewValue) {
        onAddNewValue(value)
        setIsOpen(false)
      }
    } else if (e.key === "Escape") {
      setIsOpen(false)
    }
  }

  // Handle option selection
  const handleSelect = (option: string) => {
    onChange(option)
    onSelect(option)
    setIsOpen(false)
    setHighlightedIndex(-1)
    inputRef.current?.focus()
  }

  // Handle adding new value
  const handleAddNew = () => {
    if (onAddNewValue && value.trim() !== "") {
      onAddNewValue(value)
      setIsOpen(false)
    }
  }

  return (
    <div className="relative w-full" ref={containerRef}>
      <Input
        ref={inputRef}
        type="text"
        value={value}
        onChange={(e) => {
          onChange(e.target.value)
          setIsOpen(true)
          setHighlightedIndex(-1)
        }}
        onFocus={() => setIsOpen(true)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="w-full"
      />

      {isOpen && (filteredOptions.length > 0 || showAddNew) && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
          {filteredOptions.map((option, index) => (
            <div
              key={option}
              onClick={() => handleSelect(option)}
              onMouseEnter={() => setHighlightedIndex(index)}
              className={`px-4 py-2 text-sm cursor-pointer ${
                index === highlightedIndex ? "bg-gray-100" : "hover:bg-gray-50"
              }`}
            >
              {option}
            </div>
          ))}

          {showAddNew && (
            <div
              onClick={handleAddNew}
              className={`px-4 py-2 text-sm cursor-pointer flex items-center text-blue-600 ${
                highlightedIndex === filteredOptions.length ? "bg-gray-100" : "hover:bg-gray-50"
              }`}
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Add "{value}"
            </div>
          )}
        </div>
      )}
    </div>
  )
}
