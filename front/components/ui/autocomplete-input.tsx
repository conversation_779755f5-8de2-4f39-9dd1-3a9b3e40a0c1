"use client"

import { SelectiveDropdown } from "./selective-dropdown"

interface AutocompleteInputProps {
  value: string
  onChange: (value: string) => void
  onSelect?: (value: string) => void
  options: string[]
  placeholder: string
  allowNewValues?: boolean
  onAddNewValue?: (value: string) => Promise<void>
}

// Compatibility component that wraps SelectiveDropdown
export function AutocompleteInput({
  value,
  onChange,
  onSelect,
  options,
  placeholder,
  allowNewValues = false,
  onAddNewValue
}: AutocompleteInputProps) {
  const handleAddNew = async (newValue: string): Promise<boolean> => {
    if (onAddNewValue) {
      try {
        await onAddNewValue(newValue)
        return true
      } catch (error) {
        console.error('Error adding new value:', error)
        return false
      }
    }
    return false
  }

  return (
    <SelectiveDropdown
      value={value}
      onChange={(newValue) => {
        onChange(newValue)
        onSelect?.(newValue)
      }}
      options={options}
      placeholder={placeholder}
      allowNew={allowNewValues}
      onAddNew={onAddNewValue ? handleAddNew : undefined}
    />
  )
}
