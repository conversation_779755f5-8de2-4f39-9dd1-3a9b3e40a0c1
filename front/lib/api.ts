// API utility functions for connecting to your endpoints

export interface SoftwareInventoryItem {
  id: string
  name: string
  provider: string
  // Add other fields as needed
}

export interface ClassificationResponse {
  category: string
  commercialName: string
}

export interface FeedbackData {
  itemId: string
  feedbackType: "positive" | "negative" | null
  tenantId: string
  timestamp: string
}

// Fetch software inventory for a specific tenant
export async function fetchSoftwareInventory(tenantId: string): Promise<SoftwareInventoryItem[]> {
  try {
    const response = await fetch(`/api/tenants/${tenantId}/software-inventory`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching software inventory:", error)
    throw error
  }
}

// Fetch classification for a software item
export async function fetchClassification(name: string, provider: string): Promise<ClassificationResponse> {
  try {
    const response = await fetch("/api/classify", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ name, provider }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error("Error fetching classification:", error)
    throw error
  }
}

// Send feedback data (for S3 integration or backend storage)
export async function sendFeedback(feedbackData: FeedbackData): Promise<void> {
  try {
    const response = await fetch("/api/feedback", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(feedbackData),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
  } catch (error) {
    console.error("Error sending feedback:", error)
    throw error
  }
}

// AWS S3 integration utility (you'll need to implement this based on your S3 setup)
export async function uploadToS3(data: any, key: string): Promise<void> {
  try {
    // This would typically use AWS SDK or a presigned URL
    // Example implementation:
    const response = await fetch("/api/s3-upload", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ data, key }),
    })

    if (!response.ok) {
      throw new Error(`S3 upload failed! status: ${response.status}`)
    }
  } catch (error) {
    console.error("Error uploading to S3:", error)
    throw error
  }
}
