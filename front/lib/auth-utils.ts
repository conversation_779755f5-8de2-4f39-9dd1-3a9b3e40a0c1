import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

// Simple session token (should match the one in auth/route.ts)
const SESSION_TOKEN = 'authenticated-user-session'

/**
 * Check if the request is authenticated by validating the auth token cookie
 */
export async function isAuthenticated(request?: NextRequest): Promise<boolean> {
  try {
    const cookieStore = await cookies()
    const authToken = cookieStore.get('auth-token')
    
    return authToken?.value === SESSION_TOKEN
  } catch (error) {
    console.error('Auth check error:', error)
    return false
  }
}

/**
 * Middleware function to protect API routes
 * Returns null if authenticated, or an error response if not
 */
export async function requireAuth(request?: NextRequest): Promise<NextResponse | null> {
  const authenticated = await isAuthenticated(request)
  
  if (!authenticated) {
    return NextResponse.json(
      { error: 'Authentication required', message: 'You must be logged in to access this resource' },
      { status: 401 }
    )
  }
  
  return null
}

/**
 * Higher-order function to wrap API route handlers with authentication
 */
export function withAuth<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    const authError = await requireAuth(request)
    if (authError) {
      return authError
    }
    
    return handler(request, ...args)
  }
}

/**
 * Utility to get user info from authenticated request
 * For now just returns basic info, can be extended later
 */
export async function getAuthenticatedUser(request?: NextRequest) {
  const authenticated = await isAuthenticated(request)
  
  if (!authenticated) {
    return null
  }
  
  // For now, return basic user info
  // In a real app, you'd decode JWT or lookup user from database
  return {
    username: 'admin', // This could be extracted from JWT or session
    authenticated: true
  }
}
