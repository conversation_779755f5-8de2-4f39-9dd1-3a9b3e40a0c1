import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Check if user is authenticated
  const authToken = request.cookies.get('auth-token')
  const isLoginPage = request.nextUrl.pathname === '/login'
  const isApiRoute = request.nextUrl.pathname.startsWith('/api/')
  const isAuthApiRoute = request.nextUrl.pathname === '/api/auth'

  // Allow auth API route to pass through (needed for login/logout)
  if (isAuthApiRoute) {
    return NextResponse.next()
  }

  // For other API routes, they now handle their own authentication
  // This middleware focuses on page-level protection
  if (isApiRoute) {
    return NextResponse.next()
  }

  // If user is not authenticated and not on login page, redirect to login
  if (!authToken && !isLoginPage) {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // If user is authenticated and on login page, redirect to home
  if (authToken && isLoginPage) {
    return NextResponse.redirect(new URL('/', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
